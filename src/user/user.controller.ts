import { ApiOperation } from '@nestjs/swagger';
import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { UserService } from './user.service';
import { LoginUserDto } from './dto/login-user.dto';


@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  // @Post('login')
  // @ApiOperation({ summary: 'Email + password login or register' })
  // async login(@Body() loginDto: LoginUserDto) {
  //   return await this.userService.login(loginDto);
  // }
  
}
