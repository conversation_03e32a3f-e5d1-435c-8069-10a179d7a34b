# General
PORT=

# Redis
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=

# MySQL Database
DATABASE_HOST=
DATABASE_PORT=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_NAME=
SYNC=
SEEDER_SYNC = 

ENVIRONMENT= development


# JWT
AUTH_SECRET=

# Encryption
ENCRYPTION_SECRET=

# OSS (Object Storage Service)
REGION=
ACCESS_KEY=
ACCESS_SECRET=
BUCKET=

# Exchange APIs
BINANCE_API_URL=
HATA_GLOBAL_API_URL=
HATA_MYR_API_URL=
TOKENIZE_API_URL=

# Streaming
STREAM_URL=

# Taillog (Logging Service)
TAILLOG_TOKEN=
TAILLOG_URL=

# Email
MAIL_HOST=
MAIL_PORT=
MAIL_USER=
PASSWORD=

MAX_SOCKETS = 1200
MAX_PER_IP = 600
GLOBAL_HEADROOM = 20
IDLE_LIMIT_MS=60000
SOCKET_PORT = 

